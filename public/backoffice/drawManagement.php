<?php

use models\composite\oDrawManagement\DrawRequestManager;
global $LMRId;

$drawRequestManager = new DrawRequestManager($LMRId);
$requestData = $drawRequestManager->getCategoriesDataArray();

?>
<style>
.work-table {
    background: #fff;
    border-radius: 5px;
    overflow: hidden;
}

.work-table thead {
    background: #f3f6f9;
}

.work-table th {
    font-size: 1rem !important;
    color: #495057;
    border: none;
    text-transform: capitalize;
}

.work-table td {
    padding: 1rem 0.75rem;
    border: none;
}

.work-table tbody tr:hover {
    background-color: #f8f9ff;
    transition: all 0.2s ease;
}

.category-header {
    background: #e1f0ff;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    padding: 0.75rem 1rem;
}

.line-item {
    border-bottom: 1px solid #ebedf3 !important;
}

.line-item td:first-child {
    font-weight: 600;
}

.percentage {
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    color: #fff;
    display: inline-block;
    width: 45px;
    text-align: center;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.5);
}

.tooltipClass {
    font-size: 1rem;
}
.note-btn i[data-original-title]:not([data-original-title=""]) {
    color: #007bff;
}

.note-btn {
    display: contents;
    padding: 0 !important;
    margin-left: calc(0.5rem - 0.25rem);
}

.note-btn i {
    padding: 0 !important;
}
</style>
<div class="card card-body p-0">
    <div class="card card-custom card-stretch d-flex p-0 drawManagementCard">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    Draw Management
                </h3>
            </div>
            <div class="card-toolbar ">
                <a href="javascript:void(0);"
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                    data-card-tool="toggle" data-section="drawManagementCard" data-toggle="tooltip" data-placement="top"
                    title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                    data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                    data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                    <i class="ki ki-close icon-nm"></i>
                </a>
            </div>
        </div>

        <div class="card-body p-2">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-end mb-4 mt-2">
                        <button class="btn btn-primary btn-sm" type="button">
                            <i class="fas fa-download mr-2"></i>Export Table
                        </button>
                    </div>

                    <div class="work-table">
                        <table class="table table-hover table-bordered table-vertical-center ">
                            <thead>
                                <tr>
                                    <th>Line Item</th>
                                    <th>Description</th>
                                    <th>Total Budget</th>
                                    <th>Completed Renovations</th>
                                    <th>Previously Disbursed</th>
                                    <th>% Completed</th>
                                    <th>% Requested for Draw</th>
                                    <th>Disbursement This Draw</th>
                                    <th style="width: 70px;">Lender Notes</th>
                                    <th style="width: 70px;">Borrower Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($requestData)): ?>
                                <?php foreach ($requestData as $category): ?>
                                <tr class="category-header">
                                    <td colspan="10">
                                        <?= htmlspecialchars(strtoupper($category['name'])) ?>
                                        <?php if (!empty($category['description'])): ?>
                                        <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                            title="<?= htmlspecialchars($category['description']) ?>"></i>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php if (!empty($category['lineItems'])): ?>
                                <?php foreach ($category['lineItems'] as $lineItem): ?>
                                <tr class="line-item">
                                    <td><?= htmlspecialchars($lineItem['name']) ?></td>
                                    <td><?= htmlspecialchars($lineItem['description'] ?? '') ?></td>
                                    <td>$<?= $lineItem['cost'] ?></td>
                                    <td>$<?= $lineItem['completedAmount'] ?></td>
                                    <td>$1,000</td> <!-- Previously Disbursed - hardcoded for now -->
                                    <td>
                                        <span class="percentage"><?= round($lineItem['completedPercent']) ?>%</span>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control input-sm" placeholder="10">
                                    </td>
                                    <td>
                                        <input type="text" class="form-control input-sm" placeholder="500">
                                    </td>
                                    <td style="text-align: center;">
                                        <button class="btn note-btn btn-sm" type="button">
                                            <i class="fas fa-sticky-note"></i>
                                        </button>
                                    </td>
                                    <td style="text-align: center;">
                                        <button class="btn note-btn btn-sm" type="button">
                                            <i class="fas fa-sticky-note tooltipClass" data-original-title="<?= htmlspecialchars($lineItem['notes']) ?>"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                                <?php endforeach; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="10" class="text-center text-muted py-4">
                                        No draw request data available. Please create categories and line items first.
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Action Buttons -->
<div class="d-flex justify-content-center btn-sm action-buttons">
    <input type="button" class="btn btn-primary mr-2" value="Save">
    <input type="button" class="btn btn-success save-btn" value="Submit for Approval">
</div>

<script>
$(document).ready(function() {
    $('.percentage').each(function() {
        const percentage = parseInt($(this).text());
        $(this).css('background', getPercentageColor(percentage));
    });
});

function getPercentageColor(percentage) {
    // Ensure percentage is between 0 and 100
    const p = Math.max(0, Math.min(100, percentage));

    const saturation = 100;
    const lightness = 45;



    // Hue: 0 for red (0% progress), 120 for green (100% progress)
    const hue = (p / 100) * 120;

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
}
</script>
