    <div class="card-body drawTemplateSettings collapse">
        <form name="drawTemplateSettingsForm" id="drawTemplateSettingsForm" method="post" action="api_v2/drawManagement/TemplateSettings">

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to Add / Edit Categories?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersAddEditCategories" onchange="toggleSwitch('allowBorrowersAddEditCategoriesTog','allowBorrowersAddEditCategoriesVal','1','0' );"/>
                        <span></span>
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to Remove Your Template's Categories?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersDeleteCategories" onchange="toggleSwitch('allowBorrowersDeleteCategoriesTog','allowBorrowersDeleteCategoriesVal','1','0' );"/>
                        <span></span>
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to Add / Edit Line Items?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersAddEditLineItems" onchange="toggleSwitch('allowBorrowersAddEditLineItemsTog','allowBorrowersAddEditLineItemsVal','1','0' );"/>
                        <span></span>
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to Remove Your Template's Line Items?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersDeleteLineItems" onchange="toggleSwitch('allowBorrowersDeleteLineItemsTog','allowBorrowersDeleteLineItemsVal','1','0' );"/>
                        <span></span>
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to submit for Scope of Work Revisions?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersSOWRevisions" onchange="toggleSwitch('allowBorrowersSOWRevisionsTog','allowBorrowersSOWRevisionsVal','1','0' );"/>
                        <span></span>
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to request more then rehab cost financed during a revision?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersExceedFinancedRehabCostOnRevision" onchange="toggleSwitch('allowBorrowersExceedFinancedRehabCostOnRevisionTog','allowBorrowersExceedFinancedRehabCostOnRevisionVal','1','0' );"/>
                        <span></span>
                    </label>
                </span>
            </div>
        </div>
        <div class="form-group row align-items-center">
            <label class="col-lg-4">Draw Fee</label>
            <div class=col-lg-4>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text" id="inputGroup-sizing-default">$</span>
                    </div>
                    <input type="text" class="form-control" aria-label="Draw Fees" aria-describedby="inputGroup-sizing-default" placeholder="0.00">
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-center mt-5">
            <button type="submit" class="btn btn-primary save-settings ml-2">Save</button>
        </div>
        </form>
    </div>
